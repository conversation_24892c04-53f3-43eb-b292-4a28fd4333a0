<?php
/**
 * Stripe Checkout Integration
 *
 * This file handles Stripe Checkout session creation and webhook events.
 * It provides endpoints for creating checkout sessions and processing webhook events.
 *
 * @version 1.0.0
 */

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set absolute file paths
$rootPath = __DIR__; // Current directory
require_once $rootPath . '/vendor/autoload.php';
require_once $rootPath . '/secrets.php';
require_once $rootPath . '/config.php'; // Database connection
require_once $rootPath . '/utils.php';
require_once $rootPath . '/models/ResultModel.php';

// Include Stripe modules
require_once $rootPath . '/stripe/checkout.php';
require_once $rootPath . '/stripe/customer.php';
require_once $rootPath . '/stripe/subscription.php';
require_once $rootPath . '/stripe/subscription_update.php';
require_once $rootPath . '/stripe/subscription_cooldown.php';
require_once $rootPath . '/stripe/subscription_preview.php';
require_once $rootPath . '/stripe/payment.php';
require_once $rootPath . '/stripe/payment_method.php';
require_once $rootPath . '/stripe/invoice.php';
require_once $rootPath . '/stripe/product.php';
require_once $rootPath . '/stripe/price.php';
require_once $rootPath . '/stripe/plan.php';
require_once $rootPath . '/stripe/discount.php';
require_once $rootPath . '/stripe/webhook.php';

// Apply CORS headers
cors_client();

// Check database connection
if (!isset($link) || !$link) {
    error_log("Database connection failed or \$link variable not defined");
    // Try to establish connection manually
    $link = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_DATABASE);
    if (!$link) {
        error_log("Manual connection also failed: " . mysqli_connect_error());
    } else {
        error_log("Manual connection successful");
    }
}

// Set Stripe API key
\Stripe\Stripe::setApiKey($stripeSecretKey);

// Get request method and path
$requestMethod = $_SERVER['REQUEST_METHOD'];
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);
$pathSegments = explode('/', trim($path, '/'));
$endpoint = end($pathSegments);

// Check for Stripe webhook signature header
$isStripeWebhook = isset($_SERVER['HTTP_STRIPE_SIGNATURE']) && !empty($_SERVER['HTTP_STRIPE_SIGNATURE']);

// If this is a Stripe webhook request, handle it immediately
if ($isStripeWebhook && $requestMethod === 'POST') {
    error_log("+++++ STRIPE WEBHOOK DETECTED BY SIGNATURE HEADER +++++");
    handleWebhook();
    exit;
}

// Get JSON data from request
$data = json_decode(file_get_contents('php://input'), true);

// Process based on endpoint or f parameter
if ($requestMethod === 'POST') {
    // Check if f parameter is provided in JSON data
    $action = '';

    if ($data && isset($data['f'])) {
        $action = $data['f'];
    } else {
        // If no f parameter, use endpoint from URL
        $action = $endpoint;
    }

    // Handle webhook events
    if ($action === 'webhook' || $endpoint === 'webhook') {
        handleWebhook();
    }
    // Create checkout session
    elseif ($action === 'create-checkout-session') {
        createCheckoutSession();
    }
    // Get subscription status
    elseif ($action === 'subscription-status') {
        getSubscriptionStatus();
    }
    // Cancel subscription
    elseif ($action === 'cancel-subscription') {
        cancelSubscription();
    }
    // Get subscription plans
    elseif ($action === 'get-subscription-plans') {
        getSubscriptionPlans();
    }
    // Get user subscription details
    elseif ($action === 'get-user-subscription') {
        getUserSubscription();
    }
    // Get user invoices
    elseif ($action === 'get-user-invoices') {
        getUserInvoices();
    }
    // Get user payment method
    elseif ($action === 'get-user-payment-method') {
        getUserPaymentMethod();
    }
    // Reactivate subscription
    elseif ($action === 'reactivate-subscription') {
        reactivateSubscription();
    }
    // Create payment update session
    elseif ($action === 'create-payment-update-session') {
        createPaymentUpdateSession();
    }
    // Upgrade subscription
    elseif ($action === 'upgrade-subscription') {
        upgradeSubscription();
    }
    // Preview upgrade subscription
    elseif ($action === 'preview-upgrade-subscription') {
        previewSubscriptionUpgrade();
    }
    // Get available upgrade options
    elseif ($action === 'get-upgrade-options') {
        getUpgradeOptions();
    }
    // Downgrade subscription
    elseif ($action === 'downgrade-subscription') {
        downgradeSubscription();
    }
    // Preview downgrade subscription
    elseif ($action === 'preview-downgrade-subscription') {
        previewSubscriptionDowngrade();
    }
    // Get available downgrade options
    elseif ($action === 'get-downgrade-options') {
        getDowngradeOptions();
    }
    // Get all subscription options (upgrade, downgrade, current)
    elseif ($action === 'get-subscription-options') {
        getSubscriptionOptions();
    }
    // Default response for unknown endpoints
    else {
        http_response_code(404);
        echo json_encode(['error' => 'Endpoint not found']);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}

// Note: All functions have been moved to separate files in the stripe/ directory
// If you need to create tables, use the SQL file at sql/stripe_tables.sql